---
title: Insert Or Update A Manufacture Order
description: PUT /{companyId}/manufacturing-orders
---

### Request endpoint

```http
PUT /{companyId}/manufacturing-orders
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter      | Type    | Description    |
|----------------|---------|----------------|
| fillDefaultBom | boolean | Default: false |

### Request body

**Request body schema:** `application/json`

A manufacture order to insert or update.

**Note**:
- `manufacturingOrderId` property is required, please generate a GUID when inserting.

| Field                      | Type                     | Description  |
|----------------------------|--------------------------|--------------|
| assignedToTeamMember       | object (TeamMember)      |              |
| assignedToTeamMemberId     | string <uuid> (Nullable) |              |
| completedDate              | string <date-time> (Nullable) | The date this order was completed. |
| currency                   | object (Currency)        |              |
| currencyId                 | string <uuid> (Nullable) |              |
| customFields               | object (LargeCustomFieldValues) |       |
| dueDate                    | string <date-time> (Nullable) | The due date this order should be completed by. |
| extraCosts                 | string <decimal> (Nullable) | Deprecated - use operation costs instead. Any extra costs for this order, in your home currency. |
| isCancelled                | boolean                  | Whether this order is cancelled (being cancelled voids any inventory movements) |
| isCompleted                | boolean                  | Whether this order is completed (put-away lines do not take effect until completed) |
| isPrioritized              | boolean                  | Whether this order is prioritized for completion. (This is a read-only attribute.) |
| lastModifiedBy             | object (TeamMember)      |              |
| lastModifiedById           | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this manufacture order. This is set automatically, and cannot be set through the API. |
| lines                      | Array of objects         | Lines representing the definition of the finished products and raw materials. To specify child lines within these lines, add a property called `manufacturingOrderLines` within these lines, an array recursively containing other ManufacturingOrderLines. |
| location                   | object (Location)        |              |
| locationId                 | string <uuid>            |              |
| manufacturingOrderId       | string <uuid>            | The primary identifier for this manufacture order. [When inserting, you should specify this by generating a GUID](../overview/index.md#Write-requests). Not shown to users |
| manufacturingOrderNumber   | string                   | An identifier for this manufacture order and shown on printed documents. |
| orderDate                  | string <date-time>       | The date this manufacture order was created. |
| pickLines                  | Array of objects         | Lines representing which raw materials have been picked from your warehouse |
| pickMatchings              | Array of objects         | Lines representing which pick lines correspond to which order lines |
| pickRemarks                | string                   | Any extra comments on this order regarding picking raw materials |
| primaryFinishedProduct     | object (Product)         |              |
| primaryFinishedProductId   | string <uuid> (Nullable) |              |
| putAwayRemarks             | string                   | Any extra comments on this order regarding putting finished products away |
| putLines                   | Array of objects         | Lines representing which finished products have been put away |
| remarks                    | string                   | Any extra comments on this order |
| splitPartNumber            | integer <int32> (Nullable) | Deprecated - to be deleted |
| status                     | string                   | The status of this order <br> Enum: "Open" "InProgress" "Completed" |
| timestamp                  | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| version                    | integer <int32>          | Deprecated - to be deleted |

### Payload example

> **WARNING**: The payload sample is 12646 lines long.
> Please follow the link below to see payload JSON.

[payload-sample-of-insert-or-update-a-manufacture-order.json](payload-sample-of-insert-or-update-a-manufacture-order.json)

### Response

#### Success response (200) schema: `application/json`

| Field                      | Type                     | Description  |
|----------------------------|--------------------------|--------------|
| assignedToTeamMember       | object (TeamMember)      |              |
| assignedToTeamMemberId     | string <uuid> (Nullable) |              |
| completedDate              | string <date-time> (Nullable) | The date this order was completed. |
| currency                   | object (Currency)        |              |
| currencyId                 | string <uuid> (Nullable) |              |
| customFields               | object (LargeCustomFieldValues) |       |
| dueDate                    | string <date-time> (Nullable) | The due date this order should be completed by. |
| extraCosts                 | string <decimal> (Nullable) | Deprecated - use operation costs instead. Any extra costs for this order, in your home currency. |
| isCancelled                | boolean                  | Whether this order is cancelled (being cancelled voids any inventory movements) |
| isCompleted                | boolean                  | Whether this order is completed (put-away lines do not take effect until completed) |
| isPrioritized              | boolean                  | Whether this order is prioritized for completion. (This is a read-only attribute.) |
| lastModifiedBy             | object (TeamMember)      |              |
| lastModifiedById           | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this manufacture order. This is set automatically, and cannot be set through the API. |
| lines                      | Array of objects         | Lines representing the definition of the finished products and raw materials. To specify child lines within these lines, add a property called `manufacturingOrderLines` within these lines, an array recursively containing other ManufacturingOrderLines. |
| location                   | object (Location)        |              |
| locationId                 | string <uuid>            |              |
| manufacturingOrderId       | string <uuid>            | The primary identifier for this manufacture order. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| manufacturingOrderNumber   | string                   | An identifier for this manufacture order and shown on printed documents. |
| orderDate                  | string <date-time>       | The date this manufacture order was created. |
| pickLines                  | Array of objects         | Lines representing which raw materials have been picked from your warehouse |
| pickMatchings              | Array of objects         | Lines representing which pick lines correspond to which order lines |
| pickRemarks                | string                   | Any extra comments on this order regarding picking raw materials |
| primaryFinishedProduct     | object (Product)         |              |
| primaryFinishedProductId   | string <uuid> (Nullable) |              |
| putAwayRemarks             | string                   | Any extra comments on this order regarding putting finished products away |
| putLines                   | Array of objects         | Lines representing which finished products have been put away |
| remarks                    | string                   | Any extra comments on this order |
| splitPartNumber            | integer <int32> (Nullable) | Deprecated - to be deleted |
| status                     | string                   | The status of this order <br> Enum: "Open" "InProgress" "Completed" |
| timestamp                  | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| version                    | integer <int32>          | Deprecated - to be deleted |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 12646 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-insert-or-update-a-manufacture-order.json](response-sample-of-insert-or-update-a-manufacture-order.json)
