---
title: Insert Or Update A Stock Adjustment
description: PUT /{companyId}/stock-adjustments
---

### Request endpoint

```http
PUT /{companyId}/stock-adjustments
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

A stock adjustment to insert or update

**Note**:
- `stockAdjustmentId` property is required, please generate a GUID when inserting.

| Field              | Type                     | Description          |
|--------------------|--------------------------|----------------------|
| adjustmentNumber   | string                   | An identifier for this stock adjustment and shown on printed documents. |
| adjustmentReason   | object (AdjustmentReason) |                     |
| adjustmentReasonId | string <uuid> (Nullable) |                      |
| customFields       | object (LargeCustomFieldValues) |               |
| date               | string <date-time>       | The effective date of this stock adjustment. |
| isCancelled        | boolean                  | Whether this adjustment is cancelled (being cancelled voids inventory movements) |
| lastModifiedBy     | object (TeamMember)      |                      |
| lastModifiedById   | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this stock adjustment. This is set automatically, and cannot be set through the API. |
| lines              | Array of objects         | Lines representing which inventory levels are being adjusted |
| location           | object (Location)        |                      |
| locationId         | string <uuid>            |                      |
| remarks            | string                   | Any extra comments on this stock adjustment |
| stockAdjustmentId  | string <uuid>            | The primary identifier for this stock adjustment. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| timestamp          | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

### Payload example

> **WARNING**: The payload sample is 2537 lines long.
> Please follow the link below to see payload JSON.

[payload-sample-of-insert-or-update-a-stock-adjustment.json](payload-sample-of-insert-or-update-a-stock-adjustment.json)

### Response

#### Success response (200) schema: `application/json`

| Field               | Type                     | Description         |
|---------------------|--------------------------|---------------------|
| adjustmentNumber    | string                   | An identifier for this stock adjustment and shown on printed documents. |
| adjustmentReason    | object (AdjustmentReason) |                    |
| adjustmentReasonId  | string <uuid> (Nullable) |                     |
| customFields        | object (LargeCustomFieldValues) |              |
| date                | string <date-time>       | The effective date of this stock adjustment. |
| isCancelled         | boolean                  | Whether this adjustment is cancelled (being cancelled voids inventory movements) |
| lastModifiedBy      | object (TeamMember)      |                     |
| lastModifiedById    | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this stock adjustment. This is set automatically, and cannot be set through the API. |
| lines               | Array of objects         | Lines representing which inventory levels are being adjusted |
| location            | object (Location)        |                     |
| locationId          | string <uuid>            |                     |
| remarks             | string                   | Any extra comments on this stock adjustment |
| stockAdjustmentId   | string <uuid>            | The primary identifier for this stock adjustment. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| timestamp           | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 2537 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-insert-or-update-a-stock-adjustment.json](response-sample-of-insert-or-update-a-stock-adjustment.json)
