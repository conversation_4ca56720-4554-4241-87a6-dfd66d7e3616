---
title: List Stock Adjustments
description: Relationships can be included via the include query parameter.
---

Relationships can be included via the `include` query parameter.

Options for filtering this list:
- `filter[adjustmentNumber]`

### Request endpoint

```http
GET /{companyId}/stock-adjustments
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of StockAdjustment objects. Each StockAdjustment object has the following properties:

| Field              | Type                      | Description         |
|--------------------|---------------------------|---------------------|
| adjustmentNumber   | string                    | An identifier for this stock adjustment and shown on printed documents. |
| adjustmentReason   | object (AdjustmentReason) |                     |
| adjustmentReasonId | string <uuid> (Nullable)  |                     |
| customFields       | object (LargeCustomFieldValues) |               |
| date               | string <date-time>        | The effective date of this stock adjustment. |
| isCancelled        | boolean                   | Whether this adjustment is cancelled (being cancelled voids inventory movements) |
| lastModifiedBy     | object (TeamMember)       |                     |
| lastModifiedById   | string <uuid>             | The inFlow Team Member, system process, or API key that last modified this stock adjustment. This is set automatically, and cannot be set through the API. |
| lines              | Array of objects          | Lines representing which inventory levels are being adjusted |
| location           | object (Location)         |                     |
| locationId         | string <uuid>             |                     |
| remarks            | string                    | Any extra comments on this stock adjustment |
| stockAdjustmentId  | string <uuid>             | The primary identifier for this stock adjustment. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| timestamp          | string <rowversion>       | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 2539 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-list-stock-adjustments.json](response-sample-of-list-stock-adjustments.json)
