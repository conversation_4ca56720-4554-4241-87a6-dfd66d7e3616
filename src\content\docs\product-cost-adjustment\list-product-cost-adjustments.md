---
title: List Product Cost Adjustments
description: Relationships can be included via the include query parameter.
---

Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/product-cost-adjustments
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of ProductCostAdjustment objects. Each ProductCostAdjustment object has the following properties:

| Field            | Type                 | Description |
|------------------|----------------------|-------------|
| dateTime         | string <date-time>   | The effective date of this adjustment |
| lastModifiedBy   | object (TeamMember)  |             |
| lastModifiedById | string <uuid>        | The inFlow Team Member, system process, or API key that last modified this product cost adjustment. This is set automatically, and cannot be set through the API. |
| product          | object (Product)     |             |
| productCostAdjustmentId | string <uuid> | The primary identifier for this product cost adjustment. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| productId        | string <uuid>        |             |
| serial           | string               | For serialized products, the serial number whose cost is being adjusted. |
| timestamp        | string <rowversion>  | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| unitCost         | string <decimal>     | The new target inventory cost per standard unit of measure |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 1255 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-get-product-cost-adjustments.json](response-sample-of-get-product-cost-adjustments.json)
