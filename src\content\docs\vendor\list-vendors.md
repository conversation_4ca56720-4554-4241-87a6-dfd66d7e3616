---
title: List Vendors
description: Relationships can be included via the include query parameter.
---

Relationships can be included via the `include` query parameter.

Options for filtering this list:
- `filter[name]`
- `filter[contactName]`
- `filter[phone]`
- `filter[email]`
- `filter[website]`
- `filter[address]`
- `filter[city]`
- `filter[state]`
- `filter[postalCode]`
- `filter[country]`
- `filter[isActive]`
- `filter[smart]` (search across name and contact name)

### Request endpoint

```http
GET /{companyId}/vendors
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

| Field                  | Type                   | Description        |
|------------------------|------------------------|--------------------|
| addresses              | Array of objects       | This list of all addresses (e.g. stores) for this vendor |
| balances               | Array of objects       | How much you owe this vendor (potentially in one or more currencies) |
| contactName            | string                 | Name of your primary contact for this vendor |
| credits                | Array of objects       | How much in store credit you have with this vendor (potentially in one or more currencies) |
| currency               | object (Currency)      |                    |
| currencyId             | string <uuid>          |                    |
| customFields           | object (LargeCustomFieldValues) |           |
| defaultAddress         | object (VendorAddress) |                    |
| defaultAddressId       | string <uuid> (Nullable) |                  |
| defaultCarrier         | string                 | The default shipment method for this vendor |
| defaultPaymentMethod   | string                 | The default payment method that you use to pay for purchase orders with this vendor |
| defaultPaymentTerms    | object (PaymentTerms)  |                    |
| defaultPaymentTermsId  | string <uuid> (Nullable) |                  |
| discount               | string <decimal>       | Percentage discount that you receive by default on orders to this vendor |
| dues                   | Array of objects       | How much you owe this vendor (potentially in one or more currencies) |
| email                  | string                 | Primary contact email for this vendor |
| fax                    | string                 | Fax number for this vendor |
| isActive               | boolean                | Vendors with `IsActive = false` are deactivated and hidden away for new usage. |
| isTaxInclusivePricing  | boolean                | Whether this vendor's prices are tax-inclusive, or whether tax should be added on top of those prices. |
| lastModifiedBy         | object (TeamMember)    |                    |
| lastModifiedById       | string <uuid>          | The inFlow Team Member, system process, or API key that last modified this vendor. This is set automatically, and cannot be set through the API.          |
| lastModifiedDttm       | string <date-time>     | The last time this vendor was modified. This is set automatically, and cannot be set through the API. |
| leadTimeDays           | integer <int32> (Nullable) | For reordering purposes, how many days it typically takes to receive shipments from this vendor. |
| name                   | string                 | Vendor's name (human-readable, typically a business name) |
| phone                  | string                 | Phone number for this vendor |
| remarks                | string                 | Any additional remarks regarding this vendor |
| taxingScheme           | object (TaxingScheme)  |                    |
| taxingSchemeId         | string <uuid> (Nullable) |                  |
| timestamp              | string <rowversion>    | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| vendorId               | string <uuid>          | The primary identifier for this vendor. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| vendorItems            | Array of objects       | A list of items that this vendor sells to you |
| website                | string                 | Vendor's website   |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 717 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-list-vendors.json](response-sample-of-list-vendors.json)
