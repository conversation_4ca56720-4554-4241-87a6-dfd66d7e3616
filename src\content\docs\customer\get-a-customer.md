---
title: Get A Customer
description: Relationships can be included via the include query parameter.
---

Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/customers/{customerId}
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter  | Type          | Required | Description                   |
|------------|---------------|----------|-------------------------------|
| companyId  | string <uuid> | yes      | Your inFlow account companyId |
| customerId | string <uuid> | yes      | Id of the Customer to retrieve |

### Response

#### Success response (200) schema: `application/json`

| Field                        | Type                          | Description |
|------------------------------|-------------------------------|-------------|
| addresses                    | Array of objects              | All addresses for this customer. |
| balances                     | Array of objects              | How much this customer owes you (potentially in one or more currencies) |
| contactName                  | string                        | Name of your primary contact for this customer (if it's a business) |
| credits                      | Array of objects              | How much in store credit this customer has with you (potentially in one or more currencies) |
| customFields                 | object (LargeCustomFieldValues) |  |
| customerId                   | string <uuid>                 | The primary identifier for this customer. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). Not shown to users |
| defaultBillingAddress        | object (CustomerAddress)      |  |
| defaultBillingAddressId      | string <uuid> Nullable        |  |
| defaultCarrier               | string                        | The default shipment method for this customer |
| defaultLocation              | object (Location)             |  |
| defaultLocationId            | string <uuid> Nullable        |  |
| defaultPaymentMethod         | string                        | The default payment method that this customer uses to pay you for sales orders |
| defaultPaymentTerms          | object (PaymentTerms)         |  |
| defaultPaymentTermsId        | string <uuid> Nullable        |  |
| defaultSalesRep              | string                        | The sales rep for your company that should be assigned to orders from this customer by default. Note: this can only be set when legacy free-form sales rep values are allowed. |
| defaultSalesRepTeamMember    | object (TeamMember)           |  |
| defaultSalesRepTeamMemberId  | string <uuid> Nullable        |  |
| defaultShippingAddress       | object (CustomerAddress)      |  |
| defaultShippingAddressId     | string <uuid> Nullable        |  |
| discount                     | string <decimal>              | Percentage discount that you give by default on orders by this customer |
| dues                         | Array of objects              | How much this customer owes you (potentially in one or more currencies) |
| email                        | string                        | Primary contact email for this customer |
| fax                          | string                        | Fax number for this customer |
| isActive                     | boolean                       | Vendors with `IsActive = false` are deactivated and hidden away for new usage. |
| lastModifiedBy               | object (TeamMember)           |  |
| lastModifiedById             | string <uuid>                 | The inFlow Team Member, system process, or API key that last modified this customer. This is set automatically, and cannot be set through the API. |
| lastModifiedDttm             | string <date-time>            | The DateTimeOffset when this customer was last modified. This is set automatically, and cannot be set through the API. |
| name                         | string                        | Customer's name (human-readable, typically a person or business name) |
| orderHistory                 | object (CustomerOrderHistory) |  |
| phone                        | string                        | Phone number for this customer |
| pricingScheme                | object (PricingScheme)        |  |
| pricingSchemeId              | string <uuid> Nullable        |  |
| remarks                      | string                        | Any additional remarks regarding this vendor |
| taxExemptNumber              | string                        | A government number/identifier documenting why this customer has special tax privileges |
| taxingScheme                 | object (TaxingScheme)         |  |
| taxingSchemeId               | string <uuid> Nullable        |  |
| timestamp                    | string <rowversion>           | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| website                      | string                        | Vendor's website |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 313 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-get-a-customer.json](response-sample-of-get-a-customer.json)
