---
title: Get Manufacturing Order Operation Status
description: GET /{companyId}/manufacturing-orders/{manufacturingOrderGuid}/operation-statuses
---

### Request endpoint

```http
GET /{companyId}/manufacturing-orders/{manufacturingOrderGuid}/operation-statuses
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |
| manufacturingOrderGuid | string <uuid> | yes | The manufacturingOrderGuid to be fetched |

### Response

#### Success response (200) schema: `application/json`

Array of ManufacturingOrderOperationStatus objects:

| Field                  | Type                        | Description    |
|------------------------|-----------------------------|----------------|
| completedDate          | string <date-time> Nullable | The date that this operation was completed |
| completedTeamMember    | object (TeamMember)         |                |
| completedTeamMemberId  | string <uuid>               |                |
| manufacturingOrderOperationId | string <uuid>        |                |
| manufacturingOrderOperationStatusId | string <uuid>  | Value will be operation's GUID - same as value of property ManufacturingOrderOperationId. |

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "completedDate": "2020-01-31",
    "completedTeamMember": {
      "accessAllLocations": true,
      "accessLocationIds": [
        "00000000-0000-0000-0000-000000000000"
      ],
      "accessRights": [
        "SALES_SalesOrder_View"
      ],
      "canBeSalesRep": true,
      "email": "string",
      "isActive": true,
      "isInternal": true,
      "name": "John Doe",
      "teamMemberId": "00000000-0000-0000-0000-000000000000"
    },
    "completedTeamMemberId": "00000000-0000-0000-0000-000000000000",
    "manufacturingOrderOperation": {
      "cost": "19.99",
      "instructions": "string",
      "manufacturingOrderOperationId": "00000000-0000-0000-0000-000000000000",
      "minutes": "19.99",
      "operationType": {
        "isActive": true,
        "isDefault": true,
        "name": "Assembly",
        "operationTypeId": "00000000-0000-0000-0000-000000000000",
        "timestamp": "0000000000310AB6"
      },
      "operationTypeId": "00000000-0000-0000-0000-000000000000",
      "perHourCost": "19.99",
      "remarks": "string",
      "seconds": "19.99",
      "timestamp": "0000000000310AB6"
    },
    "manufacturingOrderOperationId": "00000000-0000-0000-0000-000000000000",
    "manufacturingOrderOperationStatusId": "00000000-0000-0000-0000-000000000000"
  }
]
```
