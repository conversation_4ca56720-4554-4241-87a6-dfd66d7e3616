// @ts-check
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

import solidJs from '@astrojs/solid-js';

// https://astro.build/config
export default defineConfig({
    integrations: [starlight({
        title: 'Inflow Inventory API Documentation',
        social: [{ icon: 'github', label: 'GitHub', href: 'https://github.com/withastro/starlight' }],
        sidebar: [
            { label: 'Overview', slug: 'overview' },
            {
                label: 'Adjustment Reason',
                slug: 'adjustment-reason',
                items: [
                    { label: 'Get adjustment reasons', slug: 'adjustment-reason/get-adjustment-reasons' },
                    { label: 'List adjustment reasons', slug: 'adjustment-reason/list-adjustment-reasons' },
                ],
            },
            {
                label: 'Categories',
                slug: 'categories',
                items: [
                    { label: 'Get a category', slug: 'categories/get-a-category' },
                    { label: 'List categories', slug: 'categories/list-categories' },
                ],
            },
            {
                label: 'Currency',
                slug: 'currency',
                items: [
                    { label: 'List currencies', slug: 'currency/list-currencies' },
                    { label: 'Get a currency', slug: 'currency/get-a-currency' },
                ],
            },
            {
                label: 'Customer',
                slug: 'customer',
                items: [
                    { label: 'Get a customer', slug: 'customer/get-a-customer' },
                    { label: 'List customers', slug: 'customer/list-customers' },
                    { label: 'Insert or update a customer', slug: 'customer/insert-or-update-a-customer' },
                ],
            },
            {
                label: 'Custom Field Definitions',
                slug: 'custom-field-definitions',
                items: [
                    { label: 'Get custom field definitions', slug: 'custom-field-definitions/get-custom-field-definitions' },
                    { label: 'Insert or update custom field definition', slug: 'custom-field-definitions/insert-or-update-custom-field-definition' },
                    { label: 'Get all dropdown custom field options for an entity', slug: 'custom-field-definitions/get-all-dropdown-custom-field-options-for-an-entity' },
                    { label: 'Set custom field dropdown options', slug: 'custom-field-definitions/set-custom-field-dropdown-options' },
                ],
            },
            {
                label: 'Custom Fields',
                slug: 'custom-fields',
                items: [
                    { label: 'Get custom field labels', slug: 'custom-fields/get-custom-field-labels' },
                    { label: 'Insert or Update custom field labels', slug: 'custom-fields/insert-or-update-custom-field-labels' },
                ],
            },
            {
                label: 'Location',
                slug: 'location',
                items: [
                    { label: 'Get a location', slug: 'location/get-a-location' },
                    { label: 'List locations', slug: 'location/list-locations' },
                    { label: 'Get suggested sublocations', slug: 'location/get-suggested-sublocations' },
                    { label: 'Get suggested sublocations for a given product and location', slug: 'location/get-suggested-sublocations-for-a-given-product-and-location' },
                ],
            },
            {
                label: 'Manufacturing Order',
                slug: 'manufacturing-order',
                items: [
                    { label: 'Get a manufacture order', slug: 'manufacturing-order/get-a-manufacture-order' },
                    { label: 'Insert or update a manufacture order', slug: 'manufacturing-order/insert-or-update-a-manufacture-order' },
                    { label: 'List manufacture orders', slug: 'manufacturing-order/list-manufacture-orders' },
                    { label: 'Get manufacturing order operation status', slug: 'manufacturing-order/get-manufacturing-order-operation-status' },
                    { label: 'Insert or update manufacturing order operation status', slug: 'manufacturing-order/insert-or-update-manufacturing-order-operation-status' },
                ],
            },
            {
                label: 'Operation Type',
                slug: 'operation-type',
                items: [
                    { label: 'Get an Operation Type', slug: 'operation-type/get-an-operation-type' },
                    { label: 'List Operation Types', slug: 'operation-type/list-operation-types' },
                ],
            },
            {
                label: 'Payment Terms',
                slug: 'payment-terms',
                items: [
                    { label: 'Get payment terms', slug: 'payment-terms/get-payment-terms' },
                    { label: 'List payment terms', slug: 'payment-terms/list-payment-terms' },
                ],
            },
            {
                label: 'Pricing Scheme',
                slug: 'pricing-scheme',
                items: [
                    { label: 'Get a pricing scheme', slug: 'pricing-scheme/get-a-pricing-scheme' },
                    { label: 'List pricing schemes', slug: 'pricing-scheme/list-pricing-schemes' },
                ],
            },
            {
                label: 'Product',
                slug: 'product',
                items: [
                    { label: 'Get a product', slug: 'product/get-a-product' },
                    { label: 'List products', slug: 'product/list-products' },
                    { label: 'Insert or update product', slug: 'product/insert-or-update-product' },
                    { label: 'Get product inventory summary', slug: 'product/get-product-inventory-summary' },
                    { label: 'Get multiple product inventory summaries', slug: 'product/get-multiple-product-inventory-summaries' },
                ],
            },
            {
                label: 'Product Cost Adjustment',
                slug: 'product-cost-adjustment',
                items: [
                    { label: 'Get a product cost adjustment', slug: 'product-cost-adjustment/get-a-product-cost-adjustment' },
                    { label: 'List product cost adjustments', slug: 'product-cost-adjustment/list-product-cost-adjustments' },
                    { label: 'Insert or update product cost adjustment', slug: 'product-cost-adjustment/insert-or-update-product-cost-adjustment' },
                ],
            },
            {
                label: 'Purchase Order',
                slug: 'purchase-order',
                items: [
                    { label: 'List purchase orders', slug: 'purchase-order/list-purchase-orders' },
                    { label: 'Insert or update purchase order', slug: 'purchase-order/insert-or-update-purchase-order' },
                    { label: 'Get a purchase order', slug: 'purchase-order/get-a-purchase-order' },
                ],
            },
            {
                label: 'Sales Order',
                slug: 'sales-order',
                items: [
                    { label: 'Get a sales order', slug: 'sales-order/get-a-sales-order' },
                    { label: 'List sales orders', slug: 'sales-order/list-sales-orders' },
                    { label: 'Insert or update sales order', slug: 'sales-order/insert-or-update-sales-order' },
                ],
            },
            {
                label: 'Stock Adjustment',
                slug: 'stock-adjustment',
                items: [
                    { label: 'Get a stock adjustment', slug: 'stock-adjustment/get-a-stock-adjustment' },
                    { label: 'List stock adjustments', slug: 'stock-adjustment/list-stock-adjustments' },
                    { label: 'Insert or update a stock adjustment', slug: 'stock-adjustment/insert-or-update-a-stock-adjustment' },
                ],
            },
            {
                label: 'Stock Count',
                slug: 'stock-count',
                items: [
                    { label: 'Get a stock count', slug: 'stock-count/get-a-stock-count' },
                    { label: 'List stock counts', slug: 'stock-count/list-stock-counts' },
                    { label: 'Insert or update a stock count', slug: 'stock-count/insert-or-update-a-stock-count' },
                    { label: 'Delete a count sheet', slug: 'stock-count/delete-a-count-sheet' },
                    { label: 'Get a count sheet', slug: 'stock-count/get-a-count-sheet' },
                    { label: 'Insert or update a count sheet', slug: 'stock-count/insert-or-update-a-count-sheet' },
                ],
            },
            {
                label: 'Stockroom Scan',
                slug: 'stockroom-scan',
                items: [
                    { label: 'List stockroom scans', slug: 'stockroom-scan/list-stockroom-scans' },
                    { label: 'Insert or update a stockroom scan', slug: 'stockroom-scan/insert-or-update-a-stockroom-scan' },
                ],
            },
            {
                label: 'Stockroom User',
                slug: 'stockroom-user',
                items: [
                    { label: 'Get stockroom users', slug: 'stockroom-user/get-stockroom-users' },
                    { label: 'List stockroom users', slug: 'stockroom-user/list-stockroom-users' },
                ],
            },
            {
                label: 'Stock Transfer',
                slug: 'stock-transfer',
                items: [
                    { label: 'Get a stock transfer', slug: 'stock-transfer/get-a-stock-transfer' },
                    { label: 'List stock transfers', slug: 'stock-transfer/list-stock-transfers' },
                    { label: 'Insert or update a stock transfer', slug: 'stock-transfer/insert-or-update-a-stock-transfer' },
                ],
            },
            {
                label: 'Tax Code',
                slug: 'tax-code',
                items: [
                    { label: 'Get a tax code', slug: 'tax-code/get-a-tax-code' },
                    { label: 'List tax codes', slug: 'tax-code/list-tax-codes' },
                ],
            },
            {
                label: 'Taxing Scheme',
                slug: 'taxing-scheme',
                items: [
                    { label: 'Get a taxing scheme', slug: 'taxing-scheme/get-a-taxing-scheme' },
                    { label: 'List taxing schemes', slug: 'taxing-scheme/list-taxing-schemes' },
                    { label: 'Insert or update taxing scheme', slug: 'taxing-scheme/insert-or-update-taxing-scheme' },
                ],
            },
            {
                label: 'Team Member',
                slug: 'team-member',
                items: [
                    { label: 'List team members', slug: 'team-member/list-team-members' },
                ],
            },
            {
                label: 'Vendor',
                slug: 'vendor',
                items: [
                    { label: 'Get a vendor', slug: 'vendor/get-a-vendor' },
                    { label: 'List vendors', slug: 'vendor/list-vendors' },
                    { label: 'Insert or update a vendor', slug: 'vendor/insert-or-update-a-vendor' },
                ],
            },
            {
                label: 'Web Hooks',
                slug: 'web-hooks',
                items: [
                    { label: 'List all subscribed webhooks', slug: 'web-hooks/list-all-subscribed-webhooks' },
                    { label: 'Subscribe to a webhook', slug: 'web-hooks/subscribe-to-a-webhook' },
                    { label: 'Get a webhook subscription', slug: 'web-hooks/get-a-webhook-subscription' },
                    { label: 'Unsubscribe from a webhook', slug: 'web-hooks/unsubscribe-from-a-webhook' },
                ],
            },
        ],
		}), solidJs()],
});