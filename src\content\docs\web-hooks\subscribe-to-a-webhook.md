---
title: Subscribe To A Webhook
description: PUT /{companyId}/webhooks
---

### Request endpoint

```http
PUT /{companyId}/webhooks
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

Additional query parameter options

| Field                 | Type             | Description               |
|-----------------------|------------------|---------------------------|
| events                | Array of strings | A list of events that will trigger this webhook. |
| url                   | string           | Webhook URL to call when an event gets triggered. |
| webHookSubscriptionId | string <uuid>    | The primary identifier for this webhook. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests).                  |
| webHookSubscriptionRequestId | string <uuid> |                       |

Valid values for `events` property:
- "customer.created"
- "customer.updated"
- "vendor.created"
- "vendor.updated"
- "purchaseOrder.created"
- "purchaseOrder.updated"
- "salesOrder.created"
- "salesOrder.updated"

### Payload example

```json
{
  "events": [
    "string"
  ],
  "url": "string",
  "webHookSubscriptionId": "00000000-0000-0000-0000-000000000000",
  "webHookSubscriptionRequestId": "00000000-0000-0000-0000-000000000000"
}
```

### Response

#### Success response (200) schema: `application/json`

| Field                 | Type             | Description               |
|-----------------------|------------------|---------------------------|
| events                | Array of strings | A list of events that will trigger this webhook. |
| secret                | string           | The secret key associated with this webhook subscription, used to generate the [HMAC header that is included with each webhook request](../web-hooks/index.md). Only returned at creation. |
| url                   | string           | Webhook URL to call when an event gets triggered. |
| webHookSubscriptionId | string <uuid>    | The primary identifier for this webhook. [When inserting, you should specify this by generating a GUID](../overview/index.md#write-requests). |

Valid values for `events` property:
- "customer.created"
- "customer.updated"
- "vendor.created"
- "vendor.updated"
- "purchaseOrder.created"
- "purchaseOrder.updated"
- "salesOrder.created"
- "salesOrder.updated"

### Response sample

```json
{
  "events": [
    "string"
  ],
  "secret": "string",
  "url": "string",
  "webHookSubscriptionId": "00000000-0000-0000-0000-000000000000"
}
```
